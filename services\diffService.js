// Using the 'jsdiff' library. Make sure to install it:
// npm install jsdiff
import * as Diff from 'diff';

/**
 * Escapes HTML special characters to prevent rendering issues and XSS.
 * @param {string} unsafe The string to escape.
 * @returns {string} The escaped string.
 */
function escapeHtml(unsafe) {
  if (!unsafe) return '';
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}

/**
 * Normalizes text for better diff comparison by removing HTML tags and normalizing whitespace
 * @param {string} text The text to normalize
 * @returns {string} The normalized text
 */
function normalizeTextForDiff(text) {
  if (!text) return '';

  return text
    // Remove HTML tags (like <strong>, <em>, etc.)
    .replace(/<[^>]*>/g, '')
    // Convert markdown bold to plain text
    .replace(/\*\*(.*?)\*\*/g, '$1')
    // Normalize whitespace (multiple spaces/tabs to single space)
    .replace(/\s+/g, ' ')
    // Trim leading/trailing whitespace
    .trim();
}

/**
 * Compares two strings and generates an HTML representation of the word-level differences.
 *
 * @param {string} originalText The original text.
 * @param {string} newText The new text to compare against the original.
 * @returns {string} An HTML string with <ins> and <del> tags highlighting the differences.
 */
export function generateDiffHtml(originalText, newText) {
  // Normalize both texts for comparison
  const normalizedOriginal = normalizeTextForDiff(originalText);
  const normalizedNew = normalizeTextForDiff(newText);

  console.log('Diff comparison:');
  console.log('Original (normalized):', normalizedOriginal.substring(0, 100) + '...');
  console.log('New (normalized):', normalizedNew.substring(0, 100) + '...');

  const diff = Diff.diffWords(normalizedOriginal, normalizedNew);
  let html = '';

  diff.forEach(part => {
    const value = escapeHtml(part.value);
    if (part.added) {
      html += `<ins>${value}</ins>`;
    } else if (part.removed) {
      html += `<del>${value}</del>`;
    } else {
      html += value;
    }
  });

  return html;
}

/**
 * Generates a side-by-side diff comparison showing original and new text separately
 * @param {string} originalText The original text
 * @param {string} newText The new text to compare against the original
 * @returns {object} Object with highlightedOriginal and highlightedNew properties
 */
export function generateSideBySideDiff(originalText, newText) {
  // Normalize both texts for comparison
  const normalizedOriginal = normalizeTextForDiff(originalText);
  const normalizedNew = normalizeTextForDiff(newText);

  const diff = Diff.diffWords(normalizedOriginal, normalizedNew);

  let highlightedOriginal = '';
  let highlightedNew = '';

  diff.forEach(part => {
    const value = escapeHtml(part.value);

    if (part.added) {
      // Only show in the new text
      highlightedNew += `<ins>${value}</ins>`;
    } else if (part.removed) {
      // Only show in the original text
      highlightedOriginal += `<del>${value}</del>`;
    } else {
      // Show in both texts
      highlightedOriginal += value;
      highlightedNew += value;
    }
  });

  return {
    highlightedOriginal,
    highlightedNew
  };
}