// Using the 'jsdiff' library. Make sure to install it:
// npm install jsdiff
import * as Diff from 'diff';

/**
 * Escapes HTML special characters to prevent rendering issues and XSS.
 * @param {string} unsafe The string to escape.
 * @returns {string} The escaped string.
 */
function escapeHtml(unsafe) {
  if (!unsafe) return '';
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}

/**
 * Compares two strings and generates an HTML representation of the word-level differences.
 *
 * @param {string} originalText The original text.
 * @param {string} newText The new text to compare against the original.
 * @returns {string} An HTML string with <ins> and <del> tags highlighting the differences.
 */
export function generateDiffHtml(originalText, newText) {
  const diff = Diff.diffWords(originalText, newText);
  let html = '';

  diff.forEach(part => {
    const value = escapeHtml(part.value);
    if (part.added) {
      html += `<ins>${value}</ins>`;
    } else if (part.removed) {
      html += `<del>${value}</del>`;
    } else {
      html += value;
    }
  });

  return html;
}