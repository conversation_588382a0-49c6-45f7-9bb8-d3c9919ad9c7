/********************************************************************
 *  core.js - Abbreviated Text Expander Core Module
 *  ---------------------------------------------------------------
 *  Main initialization and coordination for the text expander
 *******************************************************************/

import * as dom from '../../domElements.js';
import * as state from '../../state.js';
import { downloadBlob } from '../../utils.js';
import { GEMINI_MODELS, DEFAULT_GEMINI_MODEL, GEMINI_API_KEY } from '../../constants.js';
import { initializeAI, testGeminiConnection } from './ai.js';
import { initializeTextProcessing } from './textProcessing.js';
import { initializeUIHandlers } from './uiHandlers.js';
import { initializeDropdowns } from './utils.js';

// Current document state
export let currentDocument = null;
export let selectedText = '';
export let selectedRange = null;
export let geminiApiKey = '';
export let documentHistory = [];
export let historyIndex = -1;
export let isSelecting = false;
export let selectionStartPos = null;

// Initialize with environment variable if available
if (GEMINI_API_KEY && !geminiApiKey) {
    geminiApiKey = GEMINI_API_KEY;
}

/**
 * Initialize the Abbreviated Text Expander
 */
export function initializeAbbreviatedTextExpander() {
    console.log('Initializing Abbreviated Text Expander...');
    
    // Initialize all modules
    initializeAI();
    initializeTextProcessing();
    initializeUIHandlers();
    initializeDropdowns();
    
    // Set up document loading
    setupDocumentHandling();
    
    // Initialize prompt template and model
    updateInstructionsPreview();
    
    console.log('Abbreviated Text Expander initialized successfully');
}

/**
 * Set up document handling
 */
function setupDocumentHandling() {
    // Listen for document changes from the main application
    document.addEventListener('documentLoaded', (event) => {
        handleDocumentLoaded(event.detail);
    });

    document.addEventListener('documentClosed', () => {
        handleDocumentClosed();
    });

    // Set up file input handler
    if (dom.abbreviatedTextExpanderFileInput) {
        dom.abbreviatedTextExpanderFileInput.addEventListener('change', handleFileInput);
    }

    // Set up save button handler
    if (dom.abbreviatedTextExpanderSaveBtn) {
        dom.abbreviatedTextExpanderSaveBtn.addEventListener('click', handleSaveDocument);
    }
}

/**
 * Handle document loaded
 */
export function handleDocumentLoaded(document) {
    currentDocument = document;
    console.log('Document loaded in Abbreviated Text Expander:', document?.name);
    
    // Reset history
    documentHistory = [];
    historyIndex = -1;
    
    // Update button states
    updateButtonStates();
    
    // Set up text area if available
    if (dom.abbreviatedTextExpanderTextArea) {
        setupTextAreaHandling();
    }
}

/**
 * Handle document closed
 */
export function handleDocumentClosed() {
    currentDocument = null;
    selectedText = '';
    selectedRange = null;
    documentHistory = [];
    historyIndex = -1;

    console.log('Document closed in Abbreviated Text Expander');
    updateButtonStates();
}

/**
 * Handle file input change
 */
async function handleFileInput(event) {
    const file = event.target.files[0];
    if (!file) return;

    if (!file.name.toLowerCase().endsWith('.docx')) {
        updateStatus("Please select a DOCX file.", "error");
        return;
    }

    try {
        updateStatus("Loading document...", "info");

        // Use mammoth.js to extract text from DOCX
        const arrayBuffer = await file.arrayBuffer();
        const result = await window.mammoth.extractRawText({ arrayBuffer });

        console.log('Mammoth extraction result:', result);
        console.log('Extracted text length:', result.value ? result.value.length : 0);
        console.log('Mammoth messages:', result.messages);

        if (result.value && result.value.trim().length > 0) {
            // Create document object
            const document = {
                file: file,
                originalText: result.value,
                name: file.name,
                modified: false
            };

            // Load the document
            handleDocumentLoaded(document);

            // Set the text content
            if (dom.abbreviatedTextExpanderTextArea) {
                dom.abbreviatedTextExpanderTextArea.textContent = result.value;
            }

            // Update file info
            if (dom.abbreviatedTextExpanderFileInfo) {
                dom.abbreviatedTextExpanderFileInfo.textContent = `Document loaded: ${file.name}`;
            }

            updateStatus("Document loaded successfully.", "success");
        } else {
            // Try alternative extraction method for html-docx-js generated files
            console.log('Primary extraction failed, trying alternative method...');
            await tryAlternativeExtraction(file);
        }
    } catch (error) {
        console.error('Error loading document:', error);
        updateStatus(`Failed to load document: ${error.message}`, "error");
    }
}

/**
 * Try alternative extraction method for html-docx-js generated files
 */
async function tryAlternativeExtraction(file) {
    try {
        console.log('Attempting alternative extraction...');

        // Try extracting with HTML preservation
        const arrayBuffer = await file.arrayBuffer();
        const htmlResult = await window.mammoth.convertToHtml({ arrayBuffer });

        console.log('HTML extraction result:', htmlResult);

        if (htmlResult.value && htmlResult.value.trim().length > 0) {
            // Convert HTML back to text but preserve some formatting
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlResult.value;

            // Get text content but preserve line breaks
            const textContent = tempDiv.textContent || tempDiv.innerText || '';

            if (textContent.trim().length > 0) {
                // Create document object
                const document = {
                    file: file,
                    originalText: textContent,
                    name: file.name,
                    modified: false
                };

                // Load the document
                handleDocumentLoaded(document);

                // Set the content (preserve HTML if it has formatting)
                if (dom.abbreviatedTextExpanderTextArea) {
                    if (htmlResult.value.includes('<strong>') || htmlResult.value.includes('<b>')) {
                        // Preserve HTML formatting
                        dom.abbreviatedTextExpanderTextArea.innerHTML = htmlResult.value;
                    } else {
                        // Use plain text
                        dom.abbreviatedTextExpanderTextArea.textContent = textContent;
                    }
                }

                // Update file info
                if (dom.abbreviatedTextExpanderFileInfo) {
                    dom.abbreviatedTextExpanderFileInfo.textContent = `Document loaded: ${file.name}`;
                }

                updateStatus("Document loaded successfully (alternative method).", "success");
                return;
            }
        }

        // If all methods fail
        updateStatus("Failed to extract text from document. The file may be corrupted or in an unsupported format.", "error");

    } catch (error) {
        console.error('Alternative extraction failed:', error);
        updateStatus("Failed to extract text from document. The file may be corrupted or in an unsupported format.", "error");
    }
}

/**
 * Handle save document
 */
async function handleSaveDocument() {
    if (!currentDocument) {
        updateStatus("No document loaded.", "error");
        return;
    }

    try {
        updateStatus("Saving document...", "info");

        // Get the current content (preserve HTML formatting)
        const htmlContent = dom.abbreviatedTextExpanderTextArea.innerHTML || dom.abbreviatedTextExpanderTextArea.textContent;

        // Process the content to ensure proper paragraph structure
        let processedContent;
        if (htmlContent.includes('<')) {
            // Content has HTML formatting - preserve it
            processedContent = htmlContent
                .replace(/<div>/gi, '<p>')
                .replace(/<\/div>/gi, '</p>')
                .replace(/<br\s*\/?>/gi, '</p><p>')
                .replace(/\n/g, '</p><p>');

            // Ensure we have proper paragraph tags
            if (!processedContent.startsWith('<p>')) {
                processedContent = '<p>' + processedContent;
            }
            if (!processedContent.endsWith('</p>')) {
                processedContent = processedContent + '</p>';
            }

            // Clean up empty paragraphs and fix double paragraphs
            processedContent = processedContent
                .replace(/<p><\/p>/g, '<p>&nbsp;</p>')
                .replace(/<p>\s*<p>/g, '<p>')
                .replace(/<\/p>\s*<\/p>/g, '</p>');
        } else {
            // Plain text content
            processedContent = htmlContent.split('\n').map(line => `<p>${line || '&nbsp;'}</p>`).join('');
        }

        const fullHtml = `
            <!DOCTYPE html>
            <html xmlns="http://www.w3.org/1999/xhtml">
            <head>
                <meta charset="utf-8">
                <title>Document</title>
                <style>
                    body {
                        font-family: 'Times New Roman', serif;
                        font-size: 12pt;
                        line-height: 1.15;
                        margin: 1in;
                    }
                    p {
                        margin: 0;
                        padding: 0;
                        margin-bottom: 6pt;
                    }
                    strong {
                        font-weight: bold;
                    }
                </style>
            </head>
            <body>
                ${processedContent}
            </body>
            </html>
        `;

        // Convert HTML to DOCX
        const docxBlob = window.htmlDocx.asBlob(fullHtml, {
            orientation: 'portrait',
            margins: {
                top: 720,    // 1 inch = 720 twips
                right: 720,
                bottom: 720,
                left: 720
            }
        });

        // Download the file
        downloadBlob(docxBlob, currentDocument.name);

        // Mark as not modified
        currentDocument.modified = false;
        updateButtonStates();

        updateStatus("Document saved successfully.", "success");
    } catch (error) {
        console.error('Error saving document:', error);
        updateStatus(`Failed to save document: ${error.message}`, "error");
    }
}

/**
 * Set up text area handling
 */
function setupTextAreaHandling() {
    if (!dom.abbreviatedTextExpanderTextArea) return;

    // Add event listeners for text selection
    dom.abbreviatedTextExpanderTextArea.addEventListener('mousedown', handleMouseDown);
    dom.abbreviatedTextExpanderTextArea.addEventListener('mouseup', handleMouseUp);
    dom.abbreviatedTextExpanderTextArea.addEventListener('selectstart', handleSelectionStart);
    dom.abbreviatedTextExpanderTextArea.addEventListener('input', handleDocumentChange);

    // Add keyboard selection detection
    dom.abbreviatedTextExpanderTextArea.addEventListener('keyup', handleKeyUp);
    dom.abbreviatedTextExpanderTextArea.addEventListener('selectionchange', handleSelectionChange);

    // Add click handler for auto-selecting paragraphs
    dom.abbreviatedTextExpanderTextArea.addEventListener('click', handleTextAreaClick);

    // Set up toolbar button event listeners
    setupToolbarButtons();

    // Add global selection change listener
    document.addEventListener('selectionchange', () => {
        // Only update if the selection is within our text area
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            if (dom.abbreviatedTextExpanderTextArea &&
                dom.abbreviatedTextExpanderTextArea.contains(range.commonAncestorContainer)) {
                setTimeout(() => {
                    updateSelectedText();
                }, 10);
            }
        }
    });
}

/**
 * Set up toolbar button event listeners
 */
function setupToolbarButtons() {
    if (dom.selectAllTextBtn) {
        dom.selectAllTextBtn.addEventListener('click', handleSelectAll);
    }

    if (dom.clearSelectionBtn) {
        dom.clearSelectionBtn.addEventListener('click', handleClearSelection);
    }

    if (dom.undoChangesBtn) {
        dom.undoChangesBtn.addEventListener('click', handleUndo);
    }

    if (dom.redoChangesBtn) {
        dom.redoChangesBtn.addEventListener('click', handleRedo);
    }
}

/**
 * Handle mouse down - start of selection
 */
function handleMouseDown(event) {
    isSelecting = true;
    selectionStartPos = getCaretPosition(event);
}

/**
 * Handle mouse up - end of selection
 */
function handleMouseUp() {
    if (isSelecting) {
        isSelecting = false;
        selectionStartPos = null;

        // Add a small delay to ensure selection is complete
        setTimeout(() => {
            updateSelectedText();
        }, 10);
    }
}

/**
 * Handle selection start event
 */
function handleSelectionStart() {
    // Allow text selection to proceed normally
    return true;
}

/**
 * Handle key up - for keyboard selection
 */
function handleKeyUp(event) {
    // Check for selection-related keys
    if (event.shiftKey || event.key === 'ArrowLeft' || event.key === 'ArrowRight' ||
        event.key === 'ArrowUp' || event.key === 'ArrowDown' ||
        event.key === 'Home' || event.key === 'End' ||
        event.ctrlKey && event.key === 'a') {
        setTimeout(() => {
            updateSelectedText();
        }, 10);
    }
}

/**
 * Handle selection change event
 */
function handleSelectionChange() {
    setTimeout(() => {
        updateSelectedText();
    }, 10);
}

/**
 * Handle text area click - auto-select paragraph if no text is selected
 */
function handleTextAreaClick(event) {
    // Small delay to let any existing selection settle
    setTimeout(() => {
        const selection = window.getSelection();

        // Only auto-select if no text is currently selected
        if (!selection.toString().trim()) {
            console.log('Auto-selecting paragraph from click...');

            // Try to select paragraph using text-based approach
            const range = document.caretRangeFromPoint(event.clientX, event.clientY);

            if (range) {
                const textNode = range.startContainer;
                console.log('Clicked node type:', textNode.nodeType, 'Content preview:', textNode.textContent?.substring(0, 50));

                if (textNode.nodeType === Node.TEXT_NODE) {
                    // Get the full text content of the entire text area
                    const fullText = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
                    const clickOffset = getAbsoluteOffset(textNode, range.startOffset);

                    console.log('Click offset in full text:', clickOffset);

                    // Find paragraph boundaries using double line breaks or significant spacing
                    const paragraphBoundaries = findParagraphBoundaries(fullText);
                    const currentParagraph = findCurrentParagraph(clickOffset, paragraphBoundaries);

                    if (currentParagraph) {
                        console.log('Found paragraph:', currentParagraph.text.substring(0, 100) + '...');
                        selectTextRange(currentParagraph.start, currentParagraph.end);
                    } else {
                        console.log('No paragraph found, selecting sentence');
                        selectCurrentSentence(event);
                    }
                } else {
                    console.log('Not a text node, trying sentence selection');
                    selectCurrentSentence(event);
                }
            }
        }
    }, 50);
}

/**
 * Get absolute offset of a text node position within the entire text area
 */
function getAbsoluteOffset(textNode, relativeOffset) {
    let offset = 0;
    const walker = document.createTreeWalker(
        dom.abbreviatedTextExpanderTextArea,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );

    let currentNode;
    while (currentNode = walker.nextNode()) {
        if (currentNode === textNode) {
            return offset + relativeOffset;
        }
        offset += currentNode.textContent.length;
    }

    return offset;
}

/**
 * Find paragraph boundaries in text using multiple strategies
 */
function findParagraphBoundaries(text) {
    const boundaries = [0]; // Start with beginning of text

    // Strategy 1: Look for double line breaks (most common)
    const doubleBreaks = text.matchAll(/\n\s*\n/g);
    for (const match of doubleBreaks) {
        boundaries.push(match.index + match[0].length);
    }

    // Strategy 2: Look for significant indentation or spacing patterns
    const lines = text.split('\n');
    let currentPos = 0;

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const nextLine = lines[i + 1];

        // Check if this looks like a paragraph break
        if (nextLine !== undefined) {
            const currentLineEndsWithPeriod = line.trim().endsWith('.');
            const nextLineStartsCapital = /^[A-Z]/.test(nextLine.trim());
            const significantGap = line.trim() === '' || nextLine.trim() === '';

            if ((currentLineEndsWithPeriod && nextLineStartsCapital) || significantGap) {
                const boundaryPos = currentPos + line.length + 1; // +1 for newline
                if (!boundaries.includes(boundaryPos)) {
                    boundaries.push(boundaryPos);
                }
            }
        }

        currentPos += line.length + 1; // +1 for newline
    }

    boundaries.push(text.length); // End with end of text
    boundaries.sort((a, b) => a - b);

    console.log('Found paragraph boundaries:', boundaries);
    return boundaries;
}

/**
 * Find which paragraph contains the given offset
 */
function findCurrentParagraph(offset, boundaries) {
    for (let i = 0; i < boundaries.length - 1; i++) {
        const start = boundaries[i];
        const end = boundaries[i + 1];

        if (offset >= start && offset < end) {
            const fullText = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
            const paragraphText = fullText.substring(start, end).trim();

            // Only return if it's a substantial paragraph (more than just whitespace)
            if (paragraphText.length > 10) {
                return {
                    start: start,
                    end: end,
                    text: paragraphText
                };
            }
        }
    }

    return null;
}

/**
 * Select text range by absolute character positions
 */
function selectTextRange(startOffset, endOffset) {
    const selection = window.getSelection();
    const range = document.createRange();

    let currentOffset = 0;
    let startNode = null, startPos = 0;
    let endNode = null, endPos = 0;

    const walker = document.createTreeWalker(
        dom.abbreviatedTextExpanderTextArea,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );

    let textNode;
    while (textNode = walker.nextNode()) {
        const nodeLength = textNode.textContent.length;

        // Find start position
        if (!startNode && currentOffset + nodeLength >= startOffset) {
            startNode = textNode;
            startPos = startOffset - currentOffset;
        }

        // Find end position
        if (!endNode && currentOffset + nodeLength >= endOffset) {
            endNode = textNode;
            endPos = endOffset - currentOffset;
            break;
        }

        currentOffset += nodeLength;
    }

    if (startNode && endNode) {
        range.setStart(startNode, Math.max(0, startPos));
        range.setEnd(endNode, Math.min(endNode.textContent.length, endPos));

        selection.removeAllRanges();
        selection.addRange(range);

        updateSelectedText();

        console.log('Selected text range:', selection.toString().substring(0, 100) + '...');
    }
}

/**
 * Select the current sentence around the click position (fallback)
 */
function selectCurrentSentence(event) {
    const selection = window.getSelection();
    const range = document.caretRangeFromPoint(event.clientX, event.clientY);

    if (range) {
        const textNode = range.startContainer;
        if (textNode.nodeType === Node.TEXT_NODE) {
            const text = textNode.textContent;
            const clickOffset = range.startOffset;

            // Find sentence boundaries (simple approach)
            let start = 0;
            let end = text.length;

            // Find start of sentence (look backwards for . ! ? or start of text)
            for (let i = clickOffset - 1; i >= 0; i--) {
                if (text[i].match(/[.!?]/)) {
                    start = i + 1;
                    break;
                }
            }

            // Find end of sentence (look forwards for . ! ?)
            for (let i = clickOffset; i < text.length; i++) {
                if (text[i].match(/[.!?]/)) {
                    end = i + 1;
                    break;
                }
            }

            // Create selection for the sentence
            const sentenceRange = document.createRange();
            sentenceRange.setStart(textNode, start);
            sentenceRange.setEnd(textNode, end);

            selection.removeAllRanges();
            selection.addRange(sentenceRange);

            updateSelectedText();

            console.log('Auto-selected sentence:', selection.toString().trim());
        }
    }
}

/**
 * Get caret position from mouse event
 */
function getCaretPosition(event) {
    if (document.caretRangeFromPoint) {
        const range = document.caretRangeFromPoint(event.clientX, event.clientY);
        return range;
    } else if (document.caretPositionFromPoint) {
        const pos = document.caretPositionFromPoint(event.clientX, event.clientY);
        if (pos) {
            const range = document.createRange();
            range.setStart(pos.offsetNode, pos.offset);
            return range;
        }
    }
    return null;
}

/**
 * Update selected text
 */
function updateSelectedText() {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
        selectedRange = selection.getRangeAt(0);
        selectedText = selectedRange.toString().trim();

        console.log('Selected text:', selectedText ? `"${selectedText}"` : 'none');
        console.log('Selection range:', selectedRange);
        console.log('Selection range collapsed:', selectedRange.collapsed);

        // If no text selected but we have a range, try to get text from the range
        if (!selectedText && selectedRange && !selectedRange.collapsed) {
            try {
                const contents = selectedRange.cloneContents();
                selectedText = contents.textContent?.trim() || '';
                console.log('Text from range contents:', selectedText);
            } catch (e) {
                console.log('Error getting text from range:', e);
            }
        }

        updateButtonStates();

        // Selection display functionality removed as per user preference
    } else {
        // No selection range, clear everything
        selectedText = '';
        selectedRange = null;
        console.log('No selection range found');
        updateButtonStates();


    }
}



/**
 * Handle document content changes
 */
export function handleDocumentChange() {
    if (!currentDocument) return;
    
    // Add to history for undo functionality
    const currentContent = dom.abbreviatedTextExpanderTextArea?.textContent || '';
    
    // Only add to history if content actually changed
    if (documentHistory.length === 0 || documentHistory[documentHistory.length - 1] !== currentContent) {
        // Remove any history after current position (for redo functionality)
        documentHistory = documentHistory.slice(0, historyIndex + 1);
        
        // Add new state
        documentHistory.push(currentContent);
        historyIndex = documentHistory.length - 1;
        
        // Limit history size
        if (documentHistory.length > 50) {
            documentHistory.shift();
            historyIndex--;
        }
    }
    
    // Mark document as modified
    if (currentDocument) {
        currentDocument.modified = true;
    }
}

/**
 * Update button states based on current state
 */
export function updateButtonStates() {
    const hasDocument = !!currentDocument;
    const hasSelection = selectedText.length > 0;
    const hasApiKey = geminiApiKey.length > 0;

    console.log('Button state update:', {
        hasDocument,
        hasSelection,
        hasApiKey,
        selectedTextLength: selectedText.length,
        geminiApiKeyLength: geminiApiKey.length,
        currentDocumentName: currentDocument?.name
    });

    // AI expansion button (requires document, selection, and API key)
    if (dom.expandAbbreviatedTextBtn) {
        const shouldEnable = hasDocument && hasSelection && hasApiKey;
        dom.expandAbbreviatedTextBtn.disabled = !shouldEnable;
        console.log('Expand abbreviations button disabled:', !shouldEnable);
    }
    
    // Test connection button removed from UI since API key is hardcoded
    
    // Highlight abbreviations button
    if (dom.highlightAbbreviationsBtn) {
        dom.highlightAbbreviationsBtn.disabled = !hasDocument;
    }
    
    // Scripture formatting button
    if (dom.formatScriptureBtn) {
        dom.formatScriptureBtn.disabled = !hasDocument;
    }
    
    // Quote formatting button
    if (dom.formatQuotesBtn) {
        dom.formatQuotesBtn.disabled = !hasDocument;
    }
    
    // Abbreviation expansion button
    if (dom.expandAbbreviationsBtn) {
        dom.expandAbbreviationsBtn.disabled = !hasDocument;
    }
    
    // Papal saints formatting button
    if (dom.formatPapalSaintsBtn) {
        dom.formatPapalSaintsBtn.disabled = !hasDocument;
    }
    
    // Footnotes formatting button
    if (dom.formatFootnotesBtn) {
        dom.formatFootnotesBtn.disabled = !hasDocument;
    }
    
    // Run all formatting button (requires document, selection, and API key since it uses AI)
    if (dom.runAllFormattingBtn) {
        dom.runAllFormattingBtn.disabled = !hasDocument || !hasSelection || !hasApiKey;
    }

    // Save button (requires document)
    if (dom.abbreviatedTextExpanderSaveBtn) {
        dom.abbreviatedTextExpanderSaveBtn.disabled = !hasDocument;
    }

    // Undo/Redo buttons
    if (dom.undoChangesBtn) {
        dom.undoChangesBtn.disabled = !hasDocument || historyIndex <= 0;
    }

    if (dom.redoChangesBtn) {
        dom.redoChangesBtn.disabled = !hasDocument || historyIndex >= documentHistory.length - 1;
    }
}

/**
 * Update instructions preview
 */
export function updateInstructionsPreview() {
    // This will be implemented in the AI module
    // For now, just a placeholder
    if (dom.currentInstructionsPreview) {
        dom.currentInstructionsPreview.innerHTML = '<p>Instructions preview will be updated by AI module</p>';
    }
}

/**
 * Update status message
 */
export function updateStatus(message, type = 'info') {
    console.log(`Status (${type}):`, message);

    // Debug: Check if status bar element exists
    console.log('Status bar element found:', !!dom.abbreviatedTextExpanderStatusBar);
    if (dom.abbreviatedTextExpanderStatusBar) {
        console.log('Status bar current text:', dom.abbreviatedTextExpanderStatusBar.textContent);
        console.log('Setting status bar to:', message);
    }

    // Update status in the abbreviated text expander status bar
    if (dom.abbreviatedTextExpanderStatusBar) {
        dom.abbreviatedTextExpanderStatusBar.textContent = message;
        dom.abbreviatedTextExpanderStatusBar.className = `status-bar ${type}`;

        // Add visual styling based on type
        dom.abbreviatedTextExpanderStatusBar.style.display = 'block';
        dom.abbreviatedTextExpanderStatusBar.style.visibility = 'visible';

        // Auto-hide after 5 seconds for non-error messages
        if (type !== 'error') {
            setTimeout(() => {
                dom.abbreviatedTextExpanderStatusBar.textContent = 'Abbreviated Text Expander Ready.';
                dom.abbreviatedTextExpanderStatusBar.className = 'status-bar';
            }, 5000);
        }
    } else {
        console.warn('Status bar element not found! Cannot display status message:', message);
    }
}

// Export state setters for other modules
export function setCurrentDocument(doc) { currentDocument = doc; }
export function setSelectedText(text) { selectedText = text; }
export function setSelectedRange(range) { selectedRange = range; }
export function setGeminiApiKey(key) { geminiApiKey = key; }
export function addToHistory(content) { 
    documentHistory.push(content);
    historyIndex = documentHistory.length - 1;
}

/**
 * Handle select all button
 */
function handleSelectAll() {
    if (!dom.abbreviatedTextExpanderTextArea) return;

    // Select all text in the editor
    const range = document.createRange();
    range.selectNodeContents(dom.abbreviatedTextExpanderTextArea);

    const selection = window.getSelection();
    selection.removeAllRanges();
    selection.addRange(range);

    // Update selected text
    updateSelectedText();

    console.log('Selected all text');
}

/**
 * Handle clear selection button
 */
function handleClearSelection() {
    // Clear the selection
    const selection = window.getSelection();
    selection.removeAllRanges();

    // Reset selected text state
    selectedText = '';
    selectedRange = null;

    // Update button states
    updateButtonStates();

    console.log('Cleared text selection');
}

/**
 * Handle undo button
 */
function handleUndo() {
    if (historyIndex > 0) {
        historyIndex--;
        const previousContent = documentHistory[historyIndex];

        if (dom.abbreviatedTextExpanderTextArea) {
            dom.abbreviatedTextExpanderTextArea.textContent = previousContent;
        }

        // Update button states
        updateButtonStates();

        console.log('Undo applied, history index:', historyIndex);
    }
}

/**
 * Handle redo button
 */
function handleRedo() {
    if (historyIndex < documentHistory.length - 1) {
        historyIndex++;
        const nextContent = documentHistory[historyIndex];

        if (dom.abbreviatedTextExpanderTextArea) {
            dom.abbreviatedTextExpanderTextArea.textContent = nextContent;
        }

        // Update button states
        updateButtonStates();

        console.log('Redo applied, history index:', historyIndex);
    }
}

// Export state getters for other modules
export function getCurrentDocument() { return currentDocument; }
export function getSelectedText() {
    // Always try to refresh selection before returning
    refreshSelection();
    return selectedText;
}
export function getSelectedRange() { return selectedRange; }
export function getGeminiApiKey() { return geminiApiKey; }
export function getDocumentHistory() { return documentHistory; }
export function getHistoryIndex() { return historyIndex; }

/**
 * Refresh the current selection
 */
export function refreshSelection() {
    updateSelectedText();
}
