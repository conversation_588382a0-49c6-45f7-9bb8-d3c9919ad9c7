/********************************************************************
 *  uiHandlers.js - UI Handlers Module
 *  ---------------------------------------------------------------
 *  Event handlers and UI management for the text expander
 *******************************************************************/

import * as dom from '../../domElements.js';
import { getCurrentDocument, getSelectedText, getSelectedRange, updateStatus, handleDocumentChange } from './core.js';
// textProcessing imports removed since we're using diffService for highlighting
import { processTextWithGemini } from './ai.js';
import { generateDiffHtml } from '../diffService.js';

// Global variable to track if run all is cancelled
let isRunAllCancelled = false;

// Global variable to track if we're showing a comparison view
let isShowingComparison = false;

/**
 * Check if we're currently showing a comparison view
 */
export function getIsShowingComparison() {
    return isShowingComparison;
}

/**
 * Initialize UI handlers
 */
export function initializeUIHandlers() {
    console.log('Initializing UI handlers...');
    
    // Set up run all formatting functionality
    if (dom.runAllFormattingBtn) {
        dom.runAllFormattingBtn.addEventListener('click', handleRunAllFormatting);
    }
    
    if (dom.cancelRunAllBtn) {
        dom.cancelRunAllBtn.addEventListener('click', handleCancelRunAll);
    }
}

/**
 * Handle run all formatting
 */
async function handleRunAllFormatting() {
    console.log('🚀 Run All Formatting button clicked!');
    updateStatus("Starting Run All Formatting...", "info");

    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }

    // Reset cancellation flag
    isRunAllCancelled = false;

    // Show progress indicator
    showRunAllProgress();

    try {
        updateRunAllProgress(10, "Preparing", "Preparing comprehensive formatting instructions...");

        // Get selected text instead of entire document
        const selectedText = getSelectedText();

        if (!selectedText || selectedText.trim().length === 0) {
            updateStatus("Please select some text to format.", "error");
            hideRunAllProgress();
            return;
        }

        updateRunAllProgress(20, "Processing", "Sending comprehensive formatting request to AI...");

        // Create the master formatting prompt that combines all instructions
        const masterPrompt = createMasterFormattingPrompt();
        console.log('Master prompt being sent to AI:', masterPrompt);
        console.log('Selected text being processed:', selectedText.substring(0, 200) + '...');

        // Process with AI using the master prompt
        const formattedContent = await processTextWithGemini(selectedText, masterPrompt);

        updateRunAllProgress(80, "Preparing Preview", "Preparing comparison view...");

        // Show comparison instead of directly applying changes
        if (formattedContent && formattedContent !== selectedText) {
            updateRunAllProgress(100, "Complete", "Review changes below");

            // Hide progress indicator and show comparison
            setTimeout(() => {
                hideRunAllProgress();
                showRunAllFormattingPreview(selectedText, formattedContent);
            }, 100);

            updateStatus("All formatting completed. Review and accept/reject changes below.", "success");
        } else {
            updateRunAllProgress(100, "Complete", "No changes needed");
            setTimeout(() => {
                hideRunAllProgress();
            }, 1000);
            updateStatus("Text processed - no formatting changes were needed.", "info");
        }

        // Hide progress after a delay
        setTimeout(() => {
            hideRunAllProgress();
        }, 2000);

    } catch (error) {
        console.error("Error in run all formatting:", error);
        updateStatus(`Failed to complete formatting: ${error.message}`, "error");
        hideRunAllProgress();
    }
}

/**
 * Create master formatting prompt that combines all formatting instructions
 */
function createMasterFormattingPrompt() {
    return `You are an expert text formatter for audiobook preparation. You MUST apply ALL of the following formatting rules to the provided text. This is a critical instruction - do not skip any rules.

Overarching Principles: Clarity and Preservation
Your primary goal is to make the text clear and easy for a narrator to read aloud. Preserve the original meaning and sentence structure. When a rule is ambiguous and could change the meaning, err on the side of caution and leave the original text unchanged.

CRITICAL Rule 1: Scripture Quote Detection and Formatting
This is the most important rule. It involves a two-step process to ensure accuracy.

1.1. Detection:
A scripture quote is identified by a piece of text (often in quotation marks) that is immediately followed by a biblical reference in parentheses.

Recognize Diverse Reference Formats: Look for standard book abbreviations (e.g., Gen, Ex, Lev, Mt, Mrk, Lk, Jn, Rom, 1 Cor, Eph, Phil, 1 Pet, Rev). References can use colons, commas, or hyphens.

Examples to recognize: (John 3:16), (1 Cor. 13:4-8), (Psalm 119:105), (Mt. 5:3, 5-7), (Lev. 17, 11).

1.2. Formatting - A Two-Step Process:

Step A: Always Expand the Reference
For ANY detected scripture reference, regardless of the quote's length, you MUST expand it using this exact spoken-word template:

Single verse: (Book, chapter [Number], verse [Number])

Verse range: (Book, chapter [Number], verses [Start] through [End])

Numbered Book: (First/Second Book, chapter [Number], verse [Number])

This is a fixed template. You must always insert the words "chapter" and "verse(s)".

Step B: Format the Quote Text (Conditional)
AFTER expanding the reference, check the word count of the quoted text itself.

If the quote is 8 or more words, you MUST:

Add "quote," at the very beginning of the quoted text.

Add "end quote," at the very end of the quoted text.

Apply bold formatting to the entire construction: the quote, the start/end markers, and the fully expanded reference from Step A.

SCRIPTURE RULE EXAMPLES:

EXAMPLE 1: Long Quote (8+ words)

BEFORE: 'Or despisest thou the riches of his goodness and patience and longsuffering? Knowest thou not that the benignity of God leadeth thee to penance?' (Rom. 2:4)

AFTER: quote, 'Or despisest thou the riches of his goodness and patience and longsuffering? Knowest thou not that the benignity of God leadeth thee to penance?' end quote, (Romans, chapter 2, verse 4)

EXAMPLE 2: Short Quote (fewer than 8 words)

BEFORE: Blood was 'the life of the flesh.' (Lev. 17, 11)

AFTER: Blood was 'the life of the flesh.' (Leviticus, chapter 17, verse 11) (Note: The reference is fully expanded, but there is no bolding and no "quote/end quote" because the word count is below 8.)

EXAMPLE 3: Verse Range

BEFORE: Charity is patient, is kind: charity envieth not, dealeth not perversely, is not puffed up. (1 Cor. 13:4-5)

AFTER: quote, Charity is patient, is kind: charity envieth not, dealeth not perversely, is not puffed up. end quote, (First Corinthians, chapter 13, verses 4 through 5)

CRITICAL Rule 2: General Abbreviation and Symbol Expansion
Expand all abbreviations into their full-word equivalents to ensure smooth narration.

Titles: Dr. → Doctor; Prof. → Professor; Mr. → Mister; Mrs. → Missus; Ms. → Mizz; Gov. → Governor

Saint (Contextual):

If St. is followed by a proper name (e.g., St. Augustine, St. Teresa), expand to Saint.

If St. is part of a place name (e.g., St. Louis), expand to Street.

Religious Orders: O.P. → Order of Preachers; S.J. → Society of Jesus; O.S.B. → Order of Saint Benedict

Measurements: ft. → feet; in. → inches; lb. → pounds; lbs. → pounds; oz. → ounces

Time: a.m. → in the morning; p.m. → in the evening (or afternoon, depending on context).

Dates: Jan. → January; Feb. → February, etc. for all months.

Common Latin: e.g. → for example; i.e. → that is; etc. → et cetera

Symbols: & → and; % → percent; § → section

CRITICAL Rule 3: Name, Title, and Numeral Formatting
Apply these formatting rules to all names, titles, and numbers.

Papal & Royal Names: When a Roman numeral follows a title and name (e.g., Pope, King, Queen), convert it to its spoken ordinal form.

Pius XII → Pius the 12th

John Paul II → John Paul the 2nd

Queen Elizabeth II → Queen Elizabeth the 2nd

Academic Degrees:

Ph.D. → Doctor of Philosophy

M.A. → Master of Arts

B.S. → Bachelor of Science

CRITICAL Rule 4: Formatting of Quotations (Non-Scriptural)
This rule applies to all formal quotations from written works like books or articles. It does not apply to conversational dialogue.

Condition: The quotation must be 15 or more words.

Detection Clues: Look for attributional phrases like "the author states," "as she wrote," "the report concludes," or the presence of a footnote marker immediately after the quote.

Action: Add "quote," at the very beginning of the quoted text and "end quote," at the very end. Do NOT apply bold formatting to these quotes.

BEFORE: In his landmark study, he noted that "the economic implications of the policy were far-reaching, affecting not only the domestic market but also international trade relations for the subsequent decade."¹

AFTER: In his landmark study, he noted that quote, "the economic implications of the policy were far-reaching, affecting not only the domestic market but also international trade relations for the subsequent decade." end quote,¹

CRITICAL Rule 5: Footnote and Endnote Formatting
Move all footnotes and endnotes to be inline with the main text.

Identify Marker: Locate the footnote marker in the text (e.g., [1], *, ^1, ¹).

Move and Format:

Find the corresponding footnote text.

Insert the entire footnote text at the location of its marker.

Enclose the inserted text in parentheses.

Begin the inserted text with a clear marker, like "Footnote:" or "Note:".

Within the footnote text, expand abbreviations or page ranges (e.g., pp. 123-126 → pages 123 through 126).

BEFORE:
Text: ...as was common in the late 19th century.¹
Footnote: 1. See Johnson, Social Mores, pp. 123-126.

AFTER: ...as was common in the late 19th century (Footnote: See Johnson, Social Mores, pages 123 through 126).

CRITICAL REQUIREMENTS:
- Return ONLY the formatted text with NO explanatory comments, introductions, or conclusions
- Do NOT add phrases like "Here is the formatted text" or "All rules applied"
- If footnote content is not provided in the original text, do NOT create placeholder footnotes
- If you cannot find the actual footnote content, simply leave the footnote marker as-is
- Your response must contain ONLY the processed text, nothing else`;
}

/**
 * Show run all formatting preview with comparison using the modal
 */
function showRunAllFormattingPreview(originalText, formattedText) {
    console.log('Showing run all formatting preview in modal...');
    console.log('Original length:', originalText.length);
    console.log('Formatted length:', formattedText.length);

    // Set flag to prevent comparison from being cleared
    isShowingComparison = true;

    // Get modal elements
    const modal = document.getElementById('text-expansion-preview-modal');
    const modalTitle = modal?.querySelector('h3');
    const originalDisplay = document.getElementById('original-text-display');
    const expandedDisplay = document.getElementById('expanded-text-display');

    console.log('Modal elements found:');
    console.log('- modal:', modal);
    console.log('- modalTitle:', modalTitle);
    console.log('- originalDisplay:', originalDisplay);
    console.log('- expandedDisplay:', expandedDisplay);

    console.log('Modal found:', !!modal);
    console.log('Original display found:', !!originalDisplay);
    console.log('Expanded display found:', !!expandedDisplay);

    if (modal && modalTitle && originalDisplay && expandedDisplay) {
        // Update modal title
        modalTitle.textContent = 'Run All Formatting Preview';

        // Convert markdown to HTML for display
        let htmlFormattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // Generate enhanced diff highlighting using diffService
        console.log('Generating enhanced diff with diffService...');
        console.log('Original text length:', originalText.length);
        console.log('Formatted text length:', htmlFormattedText.length);

        const diffHtml = generateDiffHtml(originalText, htmlFormattedText);
        console.log('Generated diff HTML length:', diffHtml.length);
        console.log('Diff HTML preview:', diffHtml.substring(0, 200) + '...');

        console.log('Setting modal content with enhanced diff...');

        // Show the original text in the left panel
        originalDisplay.innerHTML = `<div class="text-preview">${originalText}</div>`;

        // Show the diff-highlighted text in the right panel
        expandedDisplay.innerHTML = `<div class="text-preview diff-enhanced">${diffHtml}</div>`;

        // Store the formatted text for later use (use markdownText since Run All Formatting produces markdown)
        expandedDisplay.dataset.markdownText = formattedText;

        // Clear other dataset properties to ensure proper handling
        delete expandedDisplay.dataset.expandedText;
        delete expandedDisplay.dataset.formattedText;

        // Add color legend to modal if it doesn't exist
        addColorLegendToModal(modal);

        // Show the modal
        modal.style.display = 'block';

        console.log('Modal displayed successfully');



        // Set up global reset function for the modal handlers
        window.resetRunAllComparisonFlag = () => {
            isShowingComparison = false;
            console.log('Run All Formatting comparison flag reset');
        };
    } else {
        console.error('Modal elements not found');
        // Fallback to status message
        updateStatus("Comparison view could not be displayed. Modal elements missing.", "error");
        isShowingComparison = false;
    }
}

/**
 * Add color legend to modal if it doesn't exist
 */
function addColorLegendToModal(modal) {
    // Check if legend already exists
    let legend = modal.querySelector('.color-legend');

    if (!legend) {
        // Create and add legend
        legend = document.createElement('div');
        legend.className = 'color-legend';
        legend.innerHTML = `
            <div class="legend-title">Enhanced Diff View:</div>
            <div class="legend-items">
                <div class="legend-item">
                    <ins style="background-color: rgba(46, 204, 113, 0.2); color: #27ae60; text-decoration: none; padding: 2px 4px; border-radius: 3px; border: 1px solid rgba(46, 204, 113, 0.4);">Added Text</ins>
                    <span class="legend-description">New content added by AI formatting</span>
                </div>
                <div class="legend-item">
                    <del style="background-color: rgba(231, 76, 60, 0.2); color: #e74c3c; text-decoration: line-through; padding: 2px 4px; border-radius: 3px; border: 1px solid rgba(231, 76, 60, 0.4);">Removed Text</del>
                    <span class="legend-description">Original content that was replaced</span>
                </div>
                <div class="legend-item">
                    <span style="color: var(--text-color);">Unchanged Text</span>
                    <span class="legend-description">Content that remained the same</span>
                </div>
            </div>
        `;

        // Insert legend before the first text display
        const firstDisplay = modal.querySelector('.text-display');
        if (firstDisplay) {
            firstDisplay.parentNode.insertBefore(legend, firstDisplay);
        }
    }
}



/**
 * Replace selected text with formatted version
 */
function replaceSelectedText(formattedText) {
    const range = getSelectedRange();
    if (range && dom.abbreviatedTextExpanderTextArea) {
        // Delete the selected content
        range.deleteContents();

        // Insert the formatted text
        const textNode = document.createTextNode(formattedText);
        range.insertNode(textNode);

        // Clear the selection
        window.getSelection().removeAllRanges();

        console.log('Text replaced successfully');
    }
}

/**
 * Handle cancel run all
 */
function handleCancelRunAll() {
    isRunAllCancelled = true;
    updateStatus("Formatting operation cancelled.", "info");
}

/**
 * Show run all progress indicator
 */
function showRunAllProgress() {
    console.log('Showing run all progress...');
    console.log('runAllProgress element:', dom.runAllProgress);

    if (dom.runAllProgress) {
        dom.runAllProgress.style.display = 'block';
        console.log('Progress indicator displayed');
    } else {
        console.error('runAllProgress element not found!');
    }

    // Disable the run all button
    if (dom.runAllFormattingBtn) {
        dom.runAllFormattingBtn.disabled = true;
        console.log('Run all button disabled');
    }
}

/**
 * Hide run all progress indicator
 */
function hideRunAllProgress() {
    if (dom.runAllProgress) {
        dom.runAllProgress.style.display = 'none';
    }
    
    // Re-enable the run all button
    if (dom.runAllFormattingBtn && getCurrentDocument()) {
        dom.runAllFormattingBtn.disabled = false;
    }
}

/**
 * Update run all progress
 */
function updateRunAllProgress(percentage, title, description) {
    console.log(`Updating progress: ${percentage}% - ${title}: ${description}`);
    console.log('progressFill element:', dom.progressFill);
    console.log('progressText element:', dom.progressText);

    if (dom.progressFill) {
        dom.progressFill.style.width = `${percentage}%`;
        console.log(`Progress bar width set to ${percentage}%`);
    } else {
        console.error('progressFill element not found!');
    }

    if (dom.progressText) {
        dom.progressText.innerHTML = `<strong>${title}</strong><br>${description}`;
        console.log(`Progress text updated: ${title} - ${description}`);
    } else {
        console.error('progressText element not found!');
    }
}

/**
 * Generate summary of run all results
 */
function generateRunAllSummary(results) {
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    const withChanges = results.filter(r => r.success && r.hasChanges);
    
    let summary = `Run All Formatting Complete: ${successful.length}/${results.length} steps successful`;
    
    if (withChanges.length > 0) {
        summary += `. Changes applied: ${withChanges.map(r => r.step).join(', ')}`;
    }
    
    if (failed.length > 0) {
        summary += `. Failed: ${failed.map(r => r.step).join(', ')}`;
    }
    
    return summary;
}

/**
 * Initialize dropdown functionality for buttons
 */
export function initializeDropdowns() {
    const dropdownButtons = document.querySelectorAll('.button-with-dropdown');
    
    dropdownButtons.forEach(container => {
        const button = container.querySelector('button');
        const dropdownToggle = container.querySelector('.dropdown-toggle');
        
        if (dropdownToggle) {
            dropdownToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                
                // Close other dropdowns
                dropdownButtons.forEach(otherContainer => {
                    if (otherContainer !== container) {
                        otherContainer.classList.remove('expanded');
                    }
                });
                
                // Toggle current dropdown
                container.classList.toggle('expanded');
            });
        }
        
        // Prevent button click when clicking dropdown toggle
        if (button && dropdownToggle) {
            button.addEventListener('click', (e) => {
                if (e.target === dropdownToggle || dropdownToggle.contains(e.target)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            });
        }
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.button-with-dropdown')) {
            dropdownButtons.forEach(container => {
                container.classList.remove('expanded');
            });
        }
    });
}
